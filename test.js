// اختبار بسيط للتأكد من أن جميع الوحدات تعمل
console.log('🧪 بدء اختبار وحدات البوت...\n');

try {
    // اختبار تحميل الإعدادات
    console.log('📋 اختبار config.js...');
    const config = require('./config');
    console.log('✅ تم تحميل الإعدادات بنجاح');
    console.log(`   - مستويات الصعوبة: ${Object.values(config.GAME_SETTINGS.DIFFICULTY_LEVELS).join(', ')}`);
    console.log(`   - مدة الانتظار: ${config.GAME_SETTINGS.TURN_TIMEOUT} ثانية`);
    
    // اختبار قاعدة البيانات
    console.log('\n💾 اختبار database.js...');
    const database = require('./database');
    console.log('✅ تم تحميل قاعدة البيانات بنجاح');
    
    // اختبار منطق اللعبة
    console.log('\n🎮 اختبار gameLogic.js...');
    const gameLogic = require('./utils/gameLogic');
    
    // إنشاء لوحة اختبار
    const testBoard = gameLogic.createBoard();
    console.log('✅ تم إنشاء لوحة جديدة');
    
    // اختبار حركة
    const moveResult = gameLogic.makeMove(testBoard, 4, 'X');
    console.log(`✅ اختبار الحركة: ${moveResult ? 'نجح' : 'فشل'}`);
    
    // اختبار الذكاء الاصطناعي
    const botMove = gameLogic.getBestMove(testBoard, 'easy');
    console.log(`✅ اختبار الذكاء الاصطناعي: حركة البوت في الموضع ${botMove}`);
    
    // اختبار عرض اللوحة
    console.log('\n🎨 اختبار عرض اللوحة:');
    console.log(gameLogic.boardToString(testBoard));
    
    // اختبار فئة اللعبة
    console.log('\n🎯 اختبار Game.js...');
    const Game = require('./game');
    
    // إنشاء لاعب وهمي للاختبار
    const mockPlayer = {
        id: 'test123',
        displayName: 'لاعب تجريبي'
    };
    
    const testGame = new Game('test_channel', mockPlayer);
    console.log('✅ تم إنشاء لعبة تجريبية');
    console.log(`   - معرف اللعبة: ${testGame.id}`);
    console.log(`   - اللاعب الحالي: ${testGame.currentPlayer}`);
    
    // اختبار الأوامر
    console.log('\n⚡ اختبار commands/xo.js...');
    const xoCommand = require('./commands/xo');
    console.log('✅ تم تحميل أوامر اللعبة بنجاح');
    console.log(`   - اسم الأمر: ${xoCommand.data.name}`);
    console.log(`   - وصف الأمر: ${xoCommand.data.description}`);
    
    console.log('\n🎉 جميع الاختبارات نجحت! البوت جاهز للعمل.');
    console.log('\n📝 الخطوات التالية:');
    console.log('1. ضع التوكن الصحيح في config.js');
    console.log('2. شغل البوت باستخدام: npm start');
    console.log('3. استخدم الأمر /xo في Discord للعب');
    
} catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    console.error('🔧 تحقق من الملفات وحاول مرة أخرى');
}
