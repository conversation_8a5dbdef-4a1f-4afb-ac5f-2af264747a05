const fs = require('fs');
const path = require('path');
const config = require('./config');

class Database {
    constructor() {
        this.dataDir = './data';
        this.statsFile = config.DATABASE.STATS_FILE;
        this.gamesFile = config.DATABASE.GAMES_FILE;
        this.challengesFile = './data/challenges.json';

        // إنشاء مجلد البيانات إذا لم يكن موجوداً
        this.ensureDataDirectory();

        // تحميل البيانات
        this.stats = this.loadStats();
        this.games = this.loadGames();
        this.challenges = this.loadChallenges();
    }

    // التأكد من وجود مجلد البيانات
    ensureDataDirectory() {
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
    }

    // تحميل الإحصائيات
    loadStats() {
        try {
            if (fs.existsSync(this.statsFile)) {
                const data = fs.readFileSync(this.statsFile, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
        return {};
    }

    // حفظ الإحصائيات
    saveStats() {
        try {
            fs.writeFileSync(this.statsFile, JSON.stringify(this.stats, null, 2));
        } catch (error) {
            console.error('خطأ في حفظ الإحصائيات:', error);
        }
    }

    // تحميل الألعاب النشطة
    loadGames() {
        try {
            if (fs.existsSync(this.gamesFile)) {
                const data = fs.readFileSync(this.gamesFile, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            console.error('خطأ في تحميل الألعاب:', error);
        }
        return {};
    }

    // حفظ الألعاب النشطة
    saveGames() {
        try {
            fs.writeFileSync(this.gamesFile, JSON.stringify(this.games, null, 2));
        } catch (error) {
            console.error('خطأ في حفظ الألعاب:', error);
        }
    }

    // الحصول على إحصائيات لاعب
    getPlayerStats(userId) {
        if (!this.stats[userId]) {
            this.stats[userId] = {
                wins: 0,
                losses: 0,
                draws: 0,
                totalGames: 0,
                winRate: 0,
                lastPlayed: null
            };
        }
        return this.stats[userId];
    }

    // تحديث إحصائيات لاعب
    updatePlayerStats(userId, result) {
        const stats = this.getPlayerStats(userId);

        stats.totalGames++;
        stats.lastPlayed = new Date().toISOString();

        switch (result) {
            case 'win':
                stats.wins++;
                break;
            case 'loss':
                stats.losses++;
                break;
            case 'draw':
                stats.draws++;
                break;
        }

        // حساب معدل الفوز
        stats.winRate = stats.totalGames > 0 ? (stats.wins / stats.totalGames * 100).toFixed(1) : 0;

        this.saveStats();
    }

    // الحصول على لوحة المتصدرين
    getLeaderboard(limit = 10) {
        const players = Object.entries(this.stats)
            .filter(([userId, stats]) => stats.totalGames > 0)
            .sort((a, b) => {
                // ترتيب حسب معدل الفوز ثم عدد الانتصارات
                if (b[1].winRate !== a[1].winRate) {
                    return b[1].winRate - a[1].winRate;
                }
                return b[1].wins - a[1].wins;
            })
            .slice(0, limit);

        return players;
    }

    // حفظ لعبة نشطة
    saveActiveGame(gameId, gameData) {
        this.games[gameId] = gameData;
        this.saveGames();
    }

    // الحصول على لعبة نشطة
    getActiveGame(gameId) {
        return this.games[gameId] || null;
    }

    // حذف لعبة نشطة
    deleteActiveGame(gameId) {
        delete this.games[gameId];
        this.saveGames();
    }

    // الحصول على جميع الألعاب النشطة في قناة
    getChannelGames(channelId) {
        return Object.entries(this.games)
            .filter(([gameId, game]) => game.channelId === channelId)
            .map(([gameId, game]) => ({ gameId, ...game }));
    }

    // تحميل التحديات
    loadChallenges() {
        try {
            if (fs.existsSync(this.challengesFile)) {
                const data = fs.readFileSync(this.challengesFile, 'utf8');
                return JSON.parse(data);
            }
        } catch (error) {
            console.error('خطأ في تحميل التحديات:', error);
        }
        return {};
    }

    // حفظ التحديات
    saveChallenges() {
        try {
            fs.writeFileSync(this.challengesFile, JSON.stringify(this.challenges, null, 2));
        } catch (error) {
            console.error('خطأ في حفظ التحديات:', error);
        }
    }

    // إنشاء تحدي جديد
    createChallenge(challengerId, challengedId, channelId, difficulty = 'medium') {
        const challengeId = `challenge_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        this.challenges[challengeId] = {
            id: challengeId,
            challenger: challengerId,
            challenged: challengedId,
            channelId: channelId,
            difficulty: difficulty,
            status: 'pending', // pending, accepted, declined, expired
            createdAt: Date.now(),
            expiresAt: Date.now() + (config.GAME_SETTINGS.CHALLENGE_TIMEOUT * 1000)
        };

        this.saveChallenges();
        return challengeId;
    }

    // الحصول على تحدي
    getChallenge(challengeId) {
        return this.challenges[challengeId] || null;
    }

    // تحديث حالة التحدي
    updateChallengeStatus(challengeId, status) {
        if (this.challenges[challengeId]) {
            this.challenges[challengeId].status = status;
            this.saveChallenges();
            return true;
        }
        return false;
    }

    // حذف تحدي
    deleteChallenge(challengeId) {
        delete this.challenges[challengeId];
        this.saveChallenges();
    }

    // تنظيف الألعاب المنتهية الصلاحية
    cleanupExpiredGames() {
        const now = Date.now();
        const timeout = config.GAME_SETTINGS.TURN_TIMEOUT * 1000;

        Object.entries(this.games).forEach(([gameId, game]) => {
            if (now - game.lastActivity > timeout) {
                delete this.games[gameId];
            }
        });

        this.saveGames();
    }

    // تنظيف التحديات المنتهية الصلاحية
    cleanupExpiredChallenges() {
        const now = Date.now();

        Object.entries(this.challenges).forEach(([challengeId, challenge]) => {
            if (now > challenge.expiresAt && challenge.status === 'pending') {
                this.challenges[challengeId].status = 'expired';
            }
        });

        this.saveChallenges();
    }
}

module.exports = new Database();
