const { SlashCommandBuilder, EmbedBuilder } = require('discord.js');
const config = require('../config');
const database = require('../database');
const Game = require('../game');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('xo')
        .setDescription('لعب لعبة X و O (تيك توك)')
        .addUserOption(option =>
            option.setName('opponent')
                .setDescription('اللاعب الذي تريد تحديه (اتركه فارغاً للعب ضد البوت)')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('difficulty')
                .setDescription('مستوى صعوبة البوت')
                .setRequired(false)
                .addChoices(
                    { name: 'سهل 😊', value: 'easy' },
                    { name: 'متوسط 🤔', value: 'medium' },
                    { name: 'صعب 😈', value: 'hard' }
                )),

    async execute(interaction) {
        const opponent = interaction.options.getUser('opponent');
        const difficulty = interaction.options.getString('difficulty') || config.GAME_SETTINGS.DEFAULT_DIFFICULTY;
        const channel = interaction.channel;
        const user = interaction.user;

        // التحقق من الألعاب النشطة في القناة
        const activeGames = database.getChannelGames(channel.id);
        if (activeGames.length >= config.GAME_SETTINGS.MAX_GAMES_PER_CHANNEL) {
            const embed = new EmbedBuilder()
                .setColor(config.COLORS.ERROR)
                .setTitle('❌ خطأ')
                .setDescription(`لا يمكن أن يكون هناك أكثر من ${config.GAME_SETTINGS.MAX_GAMES_PER_CHANNEL} ألعاب في نفس القناة!`);

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        // التحقق من أن اللاعب لا يتحدى نفسه
        if (opponent && opponent.id === user.id) {
            const embed = new EmbedBuilder()
                .setColor(config.COLORS.ERROR)
                .setTitle('❌ خطأ')
                .setDescription('لا يمكنك تحدي نفسك! 😅');

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        // التحقق من أن الخصم ليس بوت
        if (opponent && opponent.bot) {
            const embed = new EmbedBuilder()
                .setColor(config.COLORS.ERROR)
                .setTitle('❌ خطأ')
                .setDescription('لا يمكنك تحدي بوت آخر! استخدم الأمر بدون تحديد خصم للعب ضد البوت.');

            return interaction.reply({ embeds: [embed], ephemeral: true });
        }

        // إذا كان هناك خصم، إرسال تحدي
        if (opponent) {
            return await sendChallenge(interaction, user, opponent, difficulty);
        }

        // إنشاء لعبة ضد البوت
        const game = new Game(channel.id, user, null, difficulty);

        // إنشاء الرسالة
        const embed = game.createGameEmbed();
        const buttons = game.createGameButtons();

        // إرسال الرسالة
        const message = await interaction.reply({
            embeds: [embed],
            components: buttons,
            fetchReply: true
        });

        // حفظ معرف الرسالة مع اللعبة
        game.messageId = message.id;
        game.saveGame();

        console.log(`🎮 لعبة جديدة ضد البوت بدأت: ${game.id} في القناة ${channel.name}`);
    }
};

// أمر الإحصائيات
const statsCommand = {
    data: new SlashCommandBuilder()
        .setName('xo-stats')
        .setDescription('عرض إحصائياتك في لعبة X و O')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('اللاعب الذي تريد رؤية إحصائياته')
                .setRequired(false)),

    async execute(interaction) {
        const targetUser = interaction.options.getUser('user') || interaction.user;
        const stats = database.getPlayerStats(targetUser.id);

        const embed = new EmbedBuilder()
            .setColor(config.COLORS.INFO)
            .setTitle(`📊 إحصائيات ${targetUser.displayName}`)
            .setThumbnail(targetUser.displayAvatarURL())
            .addFields(
                { name: '🏆 الانتصارات', value: stats.wins.toString(), inline: true },
                { name: '💔 الهزائم', value: stats.losses.toString(), inline: true },
                { name: '🤝 التعادل', value: stats.draws.toString(), inline: true },
                { name: '🎮 إجمالي الألعاب', value: stats.totalGames.toString(), inline: true },
                { name: '📈 معدل الفوز', value: `${stats.winRate}%`, inline: true },
                { name: '⏰ آخر لعبة', value: stats.lastPlayed ?
                    `<t:${Math.floor(new Date(stats.lastPlayed).getTime() / 1000)}:R>` :
                    'لم يلعب بعد', inline: true }
            );

        if (stats.totalGames === 0) {
            embed.setDescription('🎯 لم تلعب أي لعبة بعد! استخدم `/xo` لبدء لعبتك الأولى.');
        }

        await interaction.reply({ embeds: [embed] });
    }
};

// أمر لوحة المتصدرين
const leaderboardCommand = {
    data: new SlashCommandBuilder()
        .setName('xo-leaderboard')
        .setDescription('عرض لوحة المتصدرين في لعبة X و O'),

    async execute(interaction) {
        const leaderboard = database.getLeaderboard(config.STATS_SETTINGS.LEADERBOARD_LIMIT);

        if (leaderboard.length === 0) {
            const embed = new EmbedBuilder()
                .setColor(config.COLORS.WARNING)
                .setTitle('🏆 لوحة المتصدرين')
                .setDescription('لا توجد إحصائيات بعد! كن أول من يلعب باستخدام `/xo`');

            return interaction.reply({ embeds: [embed] });
        }

        const embed = new EmbedBuilder()
            .setColor(config.COLORS.SUCCESS)
            .setTitle('🏆 لوحة المتصدرين - X و O')
            .setDescription('أفضل اللاعبين حسب معدل الفوز:');

        let description = '';
        for (let i = 0; i < leaderboard.length; i++) {
            const [userId, stats] = leaderboard[i];
            const user = await interaction.client.users.fetch(userId).catch(() => null);
            const username = user ? user.displayName : 'مستخدم غير معروف';

            const medal = i === 0 ? '🥇' : i === 1 ? '🥈' : i === 2 ? '🥉' : `${i + 1}.`;
            description += `${medal} **${username}**\n`;
            description += `   📊 ${stats.wins}W/${stats.losses}L/${stats.draws}D (${stats.winRate}%)\n\n`;
        }

        embed.setDescription(description);
        await interaction.reply({ embeds: [embed] });
    }
};

// دالة إرسال التحدي
async function sendChallenge(interaction, challenger, challenged, difficulty) {
    const { ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    // إنشاء التحدي في قاعدة البيانات
    const challengeId = database.createChallenge(
        challenger.id,
        challenged.id,
        interaction.channel.id,
        difficulty
    );

    // إنشاء رسالة التحدي
    const challengeEmbed = new EmbedBuilder()
        .setColor(config.COLORS.WARNING)
        .setTitle('⚔️ تحدي X و O!')
        .setDescription(`**${challenger.displayName}** يتحداك في لعبة X و O!\n\n🎯 **الصعوبة:** ${difficulty}\n⏰ **انتهاء التحدي:** <t:${Math.floor((Date.now() + config.GAME_SETTINGS.CHALLENGE_TIMEOUT * 1000) / 1000)}:R>`)
        .setThumbnail(challenger.displayAvatarURL())
        .addFields(
            { name: '🎮 المتحدي', value: challenger.displayName, inline: true },
            { name: '🎯 المتحدى', value: challenged.displayName, inline: true },
            { name: '⚡ الصعوبة', value: difficulty, inline: true }
        );

    // أزرار الموافقة والرفض
    const challengeButtons = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId(`challenge_accept_${challengeId}`)
                .setLabel('قبول التحدي')
                .setEmoji('✅')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId(`challenge_decline_${challengeId}`)
                .setLabel('رفض التحدي')
                .setEmoji('❌')
                .setStyle(ButtonStyle.Danger),
            new ButtonBuilder()
                .setCustomId(`challenge_cancel_${challengeId}`)
                .setLabel('إلغاء التحدي')
                .setEmoji('🚫')
                .setStyle(ButtonStyle.Secondary)
        );

    // إرسال الرسالة
    const message = await interaction.reply({
        content: `${challenged}`, // منشن للمتحدى
        embeds: [challengeEmbed],
        components: [challengeButtons],
        fetchReply: true
    });

    // إرسال رسالة خاصة للمتحدى
    try {
        const dmEmbed = new EmbedBuilder()
            .setColor(config.COLORS.INFO)
            .setTitle('🎯 تحدي جديد!')
            .setDescription(`**${challenger.displayName}** يتحداك في لعبة X و O في سيرفر **${interaction.guild.name}**!\n\nاذهب إلى القناة للرد على التحدي.`)
            .addFields(
                { name: '🎮 المتحدي', value: challenger.displayName, inline: true },
                { name: '⚡ الصعوبة', value: difficulty, inline: true }
            );

        await challenged.send({ embeds: [dmEmbed] });
    } catch (error) {
        console.log('لا يمكن إرسال رسالة خاصة للمتحدى');
    }

    // تعيين مؤقت لانتهاء التحدي
    setTimeout(async () => {
        const challenge = database.getChallenge(challengeId);
        if (challenge && challenge.status === 'pending') {
            database.updateChallengeStatus(challengeId, 'expired');

            const expiredEmbed = new EmbedBuilder()
                .setColor(config.COLORS.ERROR)
                .setTitle('⏰ انتهت مدة التحدي')
                .setDescription('لم يتم الرد على التحدي في الوقت المحدد.');

            try {
                await message.edit({
                    embeds: [expiredEmbed],
                    components: []
                });
            } catch (error) {
                console.log('لا يمكن تحديث رسالة التحدي المنتهية');
            }
        }
    }, config.GAME_SETTINGS.CHALLENGE_TIMEOUT * 1000);

    console.log(`⚔️ تحدي جديد: ${challenger.displayName} ضد ${challenged.displayName}`);
}

module.exports.statsCommand = statsCommand;
module.exports.leaderboardCommand = leaderboardCommand;
