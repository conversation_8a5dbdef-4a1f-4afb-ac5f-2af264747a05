const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const config = require('./config');
const gameLogic = require('./utils/gameLogic');
const database = require('./database');

class Game {
    constructor(channelId, player1, player2 = null, difficulty = 'medium') {
        this.id = `${channelId}_${Date.now()}`;
        this.channelId = channelId;
        this.player1 = player1; // اللاعب الأول (X)
        this.player2 = player2; // اللاعب الثاني (O) أو null للعب ضد البوت
        this.isVsBot = player2 === null;
        this.difficulty = difficulty;
        this.board = gameLogic.createBoard();
        this.currentPlayer = 'X'; // يبدأ دائماً بـ X
        this.gameOver = false;
        this.winner = null;
        this.winningLine = null;
        this.createdAt = Date.now();
        this.lastActivity = Date.now();
        this.moveCount = 0;
    }

    // الحصول على اللاعب الحالي
    getCurrentPlayerId() {
        if (this.currentPlayer === 'X') {
            return this.player1.id;
        } else {
            return this.isVsBot ? 'bot' : this.player2.id;
        }
    }

    // الحصول على اللاعب الحالي كائن
    getCurrentPlayer() {
        if (this.currentPlayer === 'X') {
            return this.player1;
        } else {
            return this.isVsBot ? null : this.player2;
        }
    }

    // التحقق من أن اللاعب يمكنه اللعب
    canPlayerMove(userId) {
        if (this.gameOver) return false;
        if (this.isVsBot && this.currentPlayer === 'O') return false; // دور البوت
        return this.getCurrentPlayerId() === userId;
    }

    // تنفيذ حركة لاعب
    makePlayerMove(userId, position) {
        if (!this.canPlayerMove(userId)) {
            return { success: false, message: config.MESSAGES.NOT_YOUR_TURN };
        }

        if (!gameLogic.isValidMove(this.board, position)) {
            return { success: false, message: config.MESSAGES.INVALID_MOVE };
        }

        // تنفيذ الحركة
        gameLogic.makeMove(this.board, position, this.currentPlayer);
        this.moveCount++;
        this.lastActivity = Date.now();

        // التحقق من انتهاء اللعبة
        const winResult = gameLogic.checkWin(this.board);
        if (winResult) {
            this.gameOver = true;
            this.winner = winResult.winner;
            this.winningLine = winResult.winningLine;
            this.updatePlayerStats();
        } else if (gameLogic.checkDraw(this.board)) {
            this.gameOver = true;
            this.updatePlayerStats();
        } else {
            // تبديل اللاعب
            this.currentPlayer = this.currentPlayer === 'X' ? 'O' : 'X';
        }

        // حفظ حالة اللعبة
        this.saveGame();

        return { success: true, encouragement: gameLogic.getRandomEncouragement() };
    }

    // تنفيذ حركة البوت
    makeBotMove() {
        if (!this.isVsBot || this.currentPlayer !== 'O' || this.gameOver) {
            return false;
        }

        const position = gameLogic.getBestMove(this.board, this.difficulty);
        if (position === null) return false;

        // تنفيذ حركة البوت
        gameLogic.makeMove(this.board, position, 'O');
        this.moveCount++;
        this.lastActivity = Date.now();

        // التحقق من انتهاء اللعبة
        const winResult = gameLogic.checkWin(this.board);
        if (winResult) {
            this.gameOver = true;
            this.winner = winResult.winner;
            this.winningLine = winResult.winningLine;
            this.updatePlayerStats();
        } else if (gameLogic.checkDraw(this.board)) {
            this.gameOver = true;
            this.updatePlayerStats();
        } else {
            // تبديل إلى اللاعب
            this.currentPlayer = 'X';
        }

        // حفظ حالة اللعبة
        this.saveGame();

        return true;
    }

    // تحديث إحصائيات اللاعبين
    updatePlayerStats() {
        if (!config.STATS_SETTINGS.ENABLE_STATS) return;

        if (this.winner === 'X') {
            // اللاعب الأول فاز
            database.updatePlayerStats(this.player1.id, 'win');
            if (!this.isVsBot) {
                database.updatePlayerStats(this.player2.id, 'loss');
            }
        } else if (this.winner === 'O') {
            // اللاعب الثاني أو البوت فاز
            database.updatePlayerStats(this.player1.id, 'loss');
            if (!this.isVsBot) {
                database.updatePlayerStats(this.player2.id, 'win');
            }
        } else {
            // تعادل
            database.updatePlayerStats(this.player1.id, 'draw');
            if (!this.isVsBot) {
                database.updatePlayerStats(this.player2.id, 'draw');
            }
        }
    }

    // إنشاء Embed للعبة
    createGameEmbed() {
        const embed = new EmbedBuilder()
            .setColor(this.gameOver ?
                (this.winner ? config.COLORS.SUCCESS : config.COLORS.WARNING) :
                config.COLORS.PRIMARY)
            .setTitle(config.MESSAGES.GAME_START);

        // وصف اللعبة
        let description = '';

        // معلومات اللاعبين
        if (this.isVsBot) {
            description += `🎮 **${this.player1.displayName}** (❌) ضد **البوت** (⭕)\n`;
            description += `🎯 **الصعوبة:** ${this.difficulty}\n\n`;
        } else {
            description += `🎮 **${this.player1.displayName}** (❌) ضد **${this.player2.displayName}** (⭕)\n\n`;
        }

        // حالة اللعبة
        if (this.gameOver) {
            if (this.winner) {
                const winnerName = this.winner === 'X' ? this.player1.displayName :
                    (this.isVsBot ? 'البوت' : this.player2.displayName);
                description += `🎉 **${winnerName} فاز!**\n\n`;
            } else {
                description += `🤝 **تعادل!**\n\n`;
            }
        } else {
            const currentPlayerName = this.currentPlayer === 'X' ? this.player1.displayName :
                (this.isVsBot ? 'البوت' : this.player2.displayName);
            description += `🎯 **دور:** ${currentPlayerName} (${this.currentPlayer === 'X' ? '❌' : '⭕'})\n\n`;
        }

        // اللوحة
        description += '**اللوحة:**\n';
        description += '```\n';
        description += gameLogic.boardToString(this.board, this.winningLine);
        description += '\n```';

        embed.setDescription(description);

        // معلومات إضافية
        embed.addFields(
            { name: '🎲 الحركات', value: this.moveCount.toString(), inline: true },
            { name: '⏰ بدأت', value: `<t:${Math.floor(this.createdAt / 1000)}:R>`, inline: true }
        );

        return embed;
    }

    // إنشاء أزرار اللعبة
    createGameButtons() {
        const rows = [];

        for (let row = 0; row < 3; row++) {
            const actionRow = new ActionRowBuilder();

            for (let col = 0; col < 3; col++) {
                const position = row * 3 + col;
                const cell = this.board[position];

                let emoji = '⬜';
                let disabled = this.gameOver;

                if (cell === 'X') {
                    emoji = '❌';
                    disabled = true;
                } else if (cell === 'O') {
                    emoji = '⭕';
                    disabled = true;
                }

                const button = new ButtonBuilder()
                    .setCustomId(`xo_move_${this.id}_${position}`)
                    .setEmoji(emoji)
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(disabled);

                actionRow.addComponents(button);
            }

            rows.push(actionRow);
        }

        // إضافة أزرار التحكم
        if (!this.gameOver) {
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`xo_forfeit_${this.id}`)
                        .setLabel('استسلام')
                        .setEmoji('🏳️')
                        .setStyle(ButtonStyle.Danger)
                );
            rows.push(controlRow);
        } else {
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`xo_newgame_${this.channelId}`)
                        .setLabel('لعبة جديدة')
                        .setEmoji('🔄')
                        .setStyle(ButtonStyle.Success)
                );
            rows.push(controlRow);
        }

        return rows;
    }

    // حفظ اللعبة في قاعدة البيانات
    saveGame() {
        if (!this.gameOver) {
            database.saveActiveGame(this.id, {
                id: this.id, // حفظ المعرف الأصلي
                channelId: this.channelId,
                messageId: this.messageId, // حفظ معرف الرسالة
                player1: this.player1,
                player2: this.player2,
                isVsBot: this.isVsBot,
                difficulty: this.difficulty,
                board: this.board,
                currentPlayer: this.currentPlayer,
                gameOver: this.gameOver,
                winner: this.winner,
                winningLine: this.winningLine,
                createdAt: this.createdAt,
                lastActivity: this.lastActivity,
                moveCount: this.moveCount
            });
        } else {
            // حذف اللعبة المنتهية
            database.deleteActiveGame(this.id);
        }
    }

    // تحميل لعبة من قاعدة البيانات
    static loadGame(gameId) {
        const gameData = database.getActiveGame(gameId);
        if (!gameData) return null;

        const game = new Game(gameData.channelId, gameData.player1, gameData.player2, gameData.difficulty);
        game.id = gameData.id || gameId; // استخدام المعرف المحفوظ
        game.messageId = gameData.messageId; // استرجاع معرف الرسالة
        game.board = gameData.board;
        game.currentPlayer = gameData.currentPlayer;
        game.gameOver = gameData.gameOver;
        game.winner = gameData.winner;
        game.winningLine = gameData.winningLine;
        game.createdAt = gameData.createdAt;
        game.lastActivity = gameData.lastActivity;
        game.moveCount = gameData.moveCount;

        return game;
    }
}

module.exports = Game;
