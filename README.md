# 🎮 بوت Discord للعبة X و O (تيك توك)

بوت Discord متطور للعبة X و O مع ميزات متقدمة وذكاء اصطناعي!

## ✨ الميزات

### 🎯 أوضاع اللعب
- **لاعب ضد لاعب (PvP)**: تحدى أصدقاءك في السيرفر
- **لاعب ضد البوت (PvE)**: العب ضد ذكاء اصطناعي بثلاث مستويات صعوبة

### 🧠 مستويات الذكاء الاصطناعي
- **سهل 😊**: حركات عشوائية للمبتدئين
- **متوسط 🤔**: ذكاء متوسط مع بعض الاستراتيجية
- **صعب 😈**: ذكاء اصطناعي مثالي باستخدام خوارزمية Minimax

### 📊 نظام الإحصائيات
- تتبع الانتصارات والهزائم والتعادل
- حساب معدل الفوز
- لوحة المتصدرين
- إحصائيات مفصلة لكل لاعب

### 🎨 واجهة تفاعلية
- أزرار تفاعلية للعب
- رسائل Embed جميلة ومنظمة
- رموز تعبيرية واضحة
- تحديث فوري للوحة اللعبة

### ⚡ ميزات متقدمة
- دعم ألعاب متعددة في نفس الوقت
- نظام انتهاء الصلاحية للألعاب
- حفظ البيانات في ملفات JSON
- رسائل تشجيعية عشوائية
- إدارة أخطاء شاملة

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام
- Node.js 16.0.0 أو أحدث
- npm أو yarn

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد البوت

#### أ. إنشاء تطبيق Discord
1. اذهب إلى [Discord Developer Portal](https://discord.com/developers/applications)
2. انقر على "New Application"
3. اختر اسماً للتطبيق
4. اذهب إلى تبويب "Bot"
5. انقر على "Add Bot"
6. انسخ التوكن

#### ب. تعديل ملف config.js
```javascript
module.exports = {
    BOT_TOKEN: 'YOUR_BOT_TOKEN_HERE', // ضع التوكن هنا
    CLIENT_ID: 'YOUR_CLIENT_ID_HERE', // ضع معرف التطبيق هنا
    // باقي الإعدادات...
};
```

#### ج. إضافة البوت للسيرفر
1. في Developer Portal، اذهب إلى تبويب "OAuth2" > "URL Generator"
2. اختر Scopes: `bot` و `applications.commands`
3. اختر Bot Permissions: `Send Messages`, `Use Slash Commands`, `Embed Links`
4. انسخ الرابط وافتحه لإضافة البوت

### 4. تشغيل البوت
```bash
npm start
```

أو للتطوير:
```bash
npm run dev
```

## 🎮 كيفية الاستخدام

### الأوامر المتاحة

#### `/xo` - بدء لعبة جديدة
```
/xo                           # لعب ضد البوت (صعوبة متوسطة)
/xo opponent:@user            # تحدي لاعب آخر
/xo difficulty:hard           # لعب ضد البوت بصعوبة عالية
/xo opponent:@user difficulty:easy  # مع تحديد الصعوبة
```

#### `/xo-stats` - عرض الإحصائيات
```
/xo-stats                     # إحصائياتك
/xo-stats user:@user          # إحصائيات لاعب آخر
```

#### `/xo-leaderboard` - لوحة المتصدرين
```
/xo-leaderboard               # أفضل 10 لاعبين
```

### 🎮 طريقة اللعب

#### 🤖 **للعب ضد البوت:**
```
/xo                    # صعوبة متوسطة
/xo difficulty:hard    # صعوبة عالية
```

#### ⚔️ **لتحدي لاعب آخر:**
```
/xo opponent:@username                      # تحدي بصعوبة متوسطة
/xo opponent:@username difficulty:easy      # تحدي بصعوبة سهلة
```

#### 📱 **نظام التحدي الجديد:**
1. استخدم `/xo @user` لإرسال تحدي
2. يتلقى المتحدى إشعار خاص + منشن في القناة
3. المتحدى يضغط "✅ قبول التحدي" أو "❌ رفض التحدي"
4. المتحدي يمكنه الضغط على "🚫 إلغاء التحدي"
5. التحدي ينتهي تلقائياً بعد 60 ثانية
6. تبدأ اللعبة فوراً عند القبول

#### 🎯 **أثناء اللعب:**
1. انقر على الأزرار للعب في الخلايا
2. اللاعب الأول يلعب بـ ❌ والثاني بـ ⭕
3. الهدف هو الحصول على 3 رموز متطابقة في صف أو عمود أو قطر

### أزرار التحكم

#### 🎮 **أزرار اللعبة:**
- **⬜**: خلية فارغة (انقر للعب)
- **🏳️ استسلام**: الاستسلام وإنهاء اللعبة
- **🔄 لعبة جديدة**: بدء لعبة جديدة بعد انتهاء اللعبة الحالية

#### ⚔️ **أزرار التحدي:**
- **✅ قبول التحدي**: للمتحدى فقط - يبدأ اللعبة فوراً
- **❌ رفض التحدي**: للمتحدى فقط - ينهي التحدي
- **🚫 إلغاء التحدي**: للمتحدي فقط - يلغي التحدي

### 🔔 **نظام الإشعارات:**
- **منشن في القناة**: يتم منشن المتحدى في رسالة التحدي
- **رسالة خاصة**: يتلقى المتحدى إشعار خاص بالتحدي
- **انتهاء تلقائي**: التحديات تنتهي بعد 60 ثانية تلقائياً

## ⚙️ التخصيص

### تعديل الإعدادات في config.js

```javascript
// مدة انتظار اللاعب (بالثواني)
TURN_TIMEOUT: 120,

// الحد الأقصى للألعاب في القناة الواحدة
MAX_GAMES_PER_CHANNEL: 3,

// الصعوبة الافتراضية
DEFAULT_DIFFICULTY: 'medium',

// تفعيل/إلغاء الإحصائيات
ENABLE_STATS: true,

// عدد اللاعبين في لوحة المتصدرين
LEADERBOARD_LIMIT: 10
```

### تخصيص الرموز
```javascript
GAME_EMOJIS: {
    EMPTY: '🟦',     // الخلية الفارغة
    X: '❌',         // رمز X
    O: '⭕',         // رمز O
    WIN_LINE: '🟨'   // خط الفوز
}
```

### تخصيص الرسائل
```javascript
MESSAGES: {
    GAME_START: '🎮 **لعبة X و O جديدة!**',
    TURN_PLAYER: '🎯 دور {player}',
    GAME_WIN: '🎉 **{winner} فاز!**',
    // المزيد من الرسائل...
}
```

## 📁 هيكل المشروع

```
bot-xo/
├── index.js              # الملف الرئيسي
├── config.js             # إعدادات البوت
├── database.js           # إدارة قاعدة البيانات
├── game.js               # منطق اللعبة
├── package.json          # معلومات المشروع
├── README.md             # هذا الملف
├── commands/
│   └── xo.js            # أوامر اللعبة
├── utils/
│   └── gameLogic.js     # خوارزميات اللعبة
└── data/                # ملفات البيانات (تُنشأ تلقائياً)
    ├── stats.json       # إحصائيات اللاعبين
    ├── games.json       # الألعاب النشطة
    └── challenges.json  # التحديات النشطة
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### البوت لا يستجيب للأوامر
- تأكد من أن التوكن صحيح في config.js
- تأكد من أن البوت لديه صلاحيات كافية
- تحقق من أن الأوامر مسجلة بنجاح

#### الأوامر لا تظهر
- انتظر بضع دقائق (قد يستغرق Discord وقتاً لتحديث الأوامر)
- تأكد من أن CLIENT_ID صحيح
- أعد تشغيل البوت

#### خطأ في قاعدة البيانات
- تأكد من وجود مجلد `data`
- تحقق من صلاحيات الكتابة في المجلد
- احذف ملفات البيانات وأعد تشغيل البوت

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الكود
- تحسين التوثيق

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🎉 استمتع باللعب!

الآن يمكنك الاستمتاع بلعبة X و O مع أصدقائك في Discord! 🎮✨
