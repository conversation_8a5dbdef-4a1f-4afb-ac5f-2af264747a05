# 🚀 إعداد سريع لبوت X و O

## خطوات الإعداد السريعة:

### 1. إنشاء بوت Discord
1. اذهب إلى https://discord.com/developers/applications
2. انقر "New Application"
3. اختر اسماً للبوت
4. اذهب إلى تبويب "Bot"
5. <PERSON><PERSON><PERSON><PERSON> "Add Bot"
6. انسخ التوكن

### 2. تعديل الإعدادات
افتح ملف `config.js` وضع:
```javascript
BOT_TOKEN: 'التوكن_هنا',
CLIENT_ID: 'معرف_التطبيق_هنا',
```

### 3. إضافة البوت للسيرفر
1. في Developer Portal، اذهب إلى "OAuth2" > "URL Generator"
2. اختر: `bot` و `applications.commands`
3. اختر صلاحيات: `Send Messages`, `Use Slash Commands`, `Embed Links`
4. انسخ الرابط وافتحه

### 4. تشغيل البوت
```bash
npm start
```

أو استخدم ملف `start.bat` على Windows

## الأوامر:
- `/xo` - لعبة جديدة
- `/xo-stats` - الإحصائيات  
- `/xo-leaderboard` - المتصدرين

🎉 استمتع باللعب!
