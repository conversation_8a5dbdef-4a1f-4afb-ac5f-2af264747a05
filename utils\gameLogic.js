const config = require('../config');

class GameLogic {
    constructor() {
        this.WINNING_COMBINATIONS = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // صفوف
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // أعمدة
            [0, 4, 8], [2, 4, 6]             // أقطار
        ];
    }

    // إنشاء لوحة جديدة
    createBoard() {
        return Array(9).fill(null);
    }

    // التحقق من صحة الحركة
    isValidMove(board, position) {
        return position >= 0 && position < 9 && board[position] === null;
    }

    // تنفيذ حركة
    makeMove(board, position, player) {
        if (!this.isValidMove(board, position)) {
            return false;
        }
        board[position] = player;
        return true;
    }

    // التحقق من الفوز
    checkWin(board) {
        for (const combination of this.WINNING_COMBINATIONS) {
            const [a, b, c] = combination;
            if (board[a] && board[a] === board[b] && board[a] === board[c]) {
                return {
                    winner: board[a],
                    winningLine: combination
                };
            }
        }
        return null;
    }

    // التحقق من التعادل
    checkDraw(board) {
        return board.every(cell => cell !== null) && !this.checkWin(board);
    }

    // التحقق من انتهاء اللعبة
    isGameOver(board) {
        return this.checkWin(board) || this.checkDraw(board);
    }

    // الحصول على الخلايا الفارغة
    getEmptyCells(board) {
        return board.map((cell, index) => cell === null ? index : null)
                   .filter(index => index !== null);
    }

    // خوارزمية Minimax للذكاء الاصطناعي
    minimax(board, depth, isMaximizing, alpha = -Infinity, beta = Infinity) {
        const win = this.checkWin(board);

        // حالات النهاية
        if (win) {
            return win.winner === 'O' ? 10 - depth : depth - 10;
        }
        if (this.checkDraw(board)) {
            return 0;
        }

        const emptyCells = this.getEmptyCells(board);

        if (isMaximizing) {
            let maxEval = -Infinity;
            for (const cell of emptyCells) {
                board[cell] = 'O';
                const evaluation = this.minimax(board, depth + 1, false, alpha, beta);
                board[cell] = null;
                maxEval = Math.max(maxEval, evaluation);
                alpha = Math.max(alpha, evaluation);
                if (beta <= alpha) break; // Alpha-beta pruning
            }
            return maxEval;
        } else {
            let minEval = Infinity;
            for (const cell of emptyCells) {
                board[cell] = 'X';
                const evaluation = this.minimax(board, depth + 1, true, alpha, beta);
                board[cell] = null;
                minEval = Math.min(minEval, evaluation);
                beta = Math.min(beta, evaluation);
                if (beta <= alpha) break; // Alpha-beta pruning
            }
            return minEval;
        }
    }

    // الحصول على أفضل حركة للبوت
    getBestMove(board, difficulty = 'medium') {
        const emptyCells = this.getEmptyCells(board);

        if (emptyCells.length === 0) return null;

        switch (difficulty) {
            case 'easy':
                // حركة عشوائية
                return emptyCells[Math.floor(Math.random() * emptyCells.length)];

            case 'medium':
                // 70% احتمال للحركة الذكية، 30% عشوائية
                if (Math.random() < 0.7) {
                    return this.getSmartMove(board);
                } else {
                    return emptyCells[Math.floor(Math.random() * emptyCells.length)];
                }

            case 'hard':
                // حركة مثالية باستخدام Minimax
                return this.getSmartMove(board);

            default:
                return this.getSmartMove(board);
        }
    }

    // الحصول على حركة ذكية
    getSmartMove(board) {
        const emptyCells = this.getEmptyCells(board);
        let bestMove = emptyCells[0];
        let bestScore = -Infinity;

        for (const cell of emptyCells) {
            board[cell] = 'O';
            const score = this.minimax(board, 0, false);
            board[cell] = null;

            if (score > bestScore) {
                bestScore = score;
                bestMove = cell;
            }
        }

        return bestMove;
    }

    // تحويل اللوحة إلى نص للعرض
    boardToString(board, winningLine = null) {
        let result = '';

        for (let i = 0; i < 9; i++) {
            if (i % 3 === 0 && i > 0) result += '\n';

            let emoji;
            if (board[i] === 'X') {
                emoji = config.GAME_EMOJIS.X;
            } else if (board[i] === 'O') {
                emoji = config.GAME_EMOJIS.O;
            } else {
                emoji = config.GAME_EMOJIS.EMPTY;
            }

            // تمييز خط الفوز
            if (winningLine && winningLine.includes(i)) {
                emoji = config.GAME_EMOJIS.WIN_LINE;
            }

            result += emoji;
        }

        return result;
    }

    // الحصول على رسالة تشجيعية عشوائية
    getRandomEncouragement() {
        const messages = [
            config.MESSAGES.GOOD_MOVE,
            config.MESSAGES.SMART_MOVE,
            config.MESSAGES.LUCKY_MOVE
        ];
        return messages[Math.floor(Math.random() * messages.length)];
    }
}

module.exports = new GameLogic();
