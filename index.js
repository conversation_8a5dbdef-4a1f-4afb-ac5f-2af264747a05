const { Client, GatewayIntentBits, Collection, REST, Routes, EmbedBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config');
const database = require('./database');
const Game = require('./game');

// إنشاء عميل Discord
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// مجموعة الأوامر
client.commands = new Collection();

// تحميل الأوامر
function loadCommands() {
    const commandsPath = path.join(__dirname, 'commands');
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

    const commands = [];

    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);

        // تحميل الأمر الرئيسي
        if (command.data && command.execute) {
            client.commands.set(command.data.name, command);
            commands.push(command.data.toJSON());
        }

        // تحميل الأوامر الإضافية
        if (command.statsCommand) {
            client.commands.set(command.statsCommand.data.name, command.statsCommand);
            commands.push(command.statsCommand.data.toJSON());
        }

        if (command.leaderboardCommand) {
            client.commands.set(command.leaderboardCommand.data.name, command.leaderboardCommand);
            commands.push(command.leaderboardCommand.data.toJSON());
        }
    }

    return commands;
}

// تسجيل الأوامر
async function registerCommands() {
    try {
        console.log('🔄 بدء تسجيل أوامر التطبيق...');

        const commands = loadCommands();
        const rest = new REST({ version: '10' }).setToken(config.BOT_TOKEN);

        await rest.put(
            Routes.applicationCommands(config.CLIENT_ID),
            { body: commands }
        );

        console.log(`✅ تم تسجيل ${commands.length} أوامر بنجاح!`);
    } catch (error) {
        console.error('❌ خطأ في تسجيل الأوامر:', error);
    }
}

// عند جاهزية البوت
client.once('ready', async () => {
    console.log(`🤖 البوت ${client.user.tag} جاهز!`);
    console.log(`🎮 متصل بـ ${client.guilds.cache.size} سيرفر`);

    // تنظيف الألعاب والتحديات المنتهية الصلاحية
    database.cleanupExpiredGames();
    database.cleanupExpiredChallenges();

    // تنظيف دوري كل 5 دقائق
    setInterval(() => {
        database.cleanupExpiredGames();
        database.cleanupExpiredChallenges();
    }, 5 * 60 * 1000);

    // تسجيل الأوامر
    await registerCommands();
});

// معالجة أوامر التطبيق
client.on('interactionCreate', async interaction => {
    if (interaction.isChatInputCommand()) {
        const command = client.commands.get(interaction.commandName);

        if (!command) {
            console.error(`❌ لم يتم العثور على الأمر ${interaction.commandName}`);
            return;
        }

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error('❌ خطأ في تنفيذ الأمر:', error);

            const errorEmbed = new EmbedBuilder()
                .setColor(config.COLORS.ERROR)
                .setTitle('❌ خطأ')
                .setDescription('حدث خطأ أثناء تنفيذ الأمر!');

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    }

    // معالجة أزرار اللعبة
    if (interaction.isButton()) {
        await handleButtonInteraction(interaction);
    }
});

// معالجة تفاعلات الأزرار
async function handleButtonInteraction(interaction) {
    const customId = interaction.customId;

    // حركة في اللعبة
    if (customId.startsWith('xo_move_')) {
        const parts = customId.split('_');
        const gameId = parts[2];
        const position = parseInt(parts[3]);

        const game = Game.loadGame(gameId);
        if (!game) {
            return interaction.reply({
                content: '❌ هذه اللعبة لم تعد موجودة!',
                ephemeral: true
            });
        }

        // تنفيذ حركة اللاعب
        const result = game.makePlayerMove(interaction.user.id, position);

        if (!result.success) {
            return interaction.reply({
                content: result.message,
                ephemeral: true
            });
        }

        // تحديث الرسالة
        const embed = game.createGameEmbed();
        const buttons = game.createGameButtons();

        await interaction.update({
            embeds: [embed],
            components: buttons
        });

        // إذا كانت اللعبة ضد البوت ولم تنته، دع البوت يلعب
        if (game.isVsBot && !game.gameOver && game.currentPlayer === 'O') {
            setTimeout(async () => {
                const botMoved = game.makeBotMove();
                if (botMoved) {
                    const newEmbed = game.createGameEmbed();
                    const newButtons = game.createGameButtons();

                    await interaction.editReply({
                        embeds: [newEmbed],
                        components: newButtons
                    });
                }
            }, 1000); // تأخير ثانية واحدة لمحاكاة تفكير البوت
        }
    }

    // الاستسلام
    else if (customId.startsWith('xo_forfeit_')) {
        const gameId = customId.split('_')[2];
        const game = Game.loadGame(gameId);

        if (!game) {
            return interaction.reply({
                content: '❌ هذه اللعبة لم تعد موجودة!',
                ephemeral: true
            });
        }

        // التحقق من أن اللاعب يمكنه الاستسلام
        if (game.player1.id !== interaction.user.id &&
            (!game.player2 || game.player2.id !== interaction.user.id)) {
            return interaction.reply({
                content: config.MESSAGES.NOT_YOUR_GAME,
                ephemeral: true
            });
        }

        // تحديد الفائز (الخصم)
        if (game.player1.id === interaction.user.id) {
            game.winner = 'O';
        } else {
            game.winner = 'X';
        }

        game.gameOver = true;
        game.updatePlayerStats();

        const embed = game.createGameEmbed();
        const buttons = game.createGameButtons();

        await interaction.update({
            embeds: [embed],
            components: buttons
        });

        // حذف اللعبة من قاعدة البيانات
        database.deleteActiveGame(game.id);
    }

    // لعبة جديدة
    else if (customId.startsWith('xo_newgame_')) {
        const channelId = customId.split('_')[2];

        // التحقق من الألعاب النشطة
        const activeGames = database.getChannelGames(channelId);
        if (activeGames.length >= config.GAME_SETTINGS.MAX_GAMES_PER_CHANNEL) {
            return interaction.reply({
                content: `❌ لا يمكن أن يكون هناك أكثر من ${config.GAME_SETTINGS.MAX_GAMES_PER_CHANNEL} ألعاب في نفس القناة!`,
                ephemeral: true
            });
        }

        // إنشاء لعبة جديدة
        const newGame = new Game(channelId, interaction.user);

        const embed = newGame.createGameEmbed();
        const buttons = newGame.createGameButtons();

        const message = await interaction.reply({
            embeds: [embed],
            components: buttons,
            fetchReply: true
        });

        newGame.messageId = message.id;
        newGame.saveGame();
    }

    // معالجة أزرار التحدي
    else if (customId.startsWith('challenge_')) {
        await handleChallengeButton(interaction);
    }
}

// معالجة أزرار التحدي
async function handleChallengeButton(interaction) {
    const customId = interaction.customId;
    const parts = customId.split('_');
    const action = parts[1]; // accept, decline, cancel
    const challengeId = parts[2];

    const challenge = database.getChallenge(challengeId);
    if (!challenge) {
        return interaction.reply({
            content: '❌ هذا التحدي لم يعد موجوداً!',
            ephemeral: true
        });
    }

    // التحقق من انتهاء صلاحية التحدي
    if (challenge.status !== 'pending') {
        return interaction.reply({
            content: '❌ هذا التحدي لم يعد نشطاً!',
            ephemeral: true
        });
    }

    const challenger = await interaction.client.users.fetch(challenge.challenger);
    const challenged = await interaction.client.users.fetch(challenge.challenged);

    if (action === 'accept') {
        // يجب أن يكون المتحدى هو من يقبل
        if (interaction.user.id !== challenge.challenged) {
            return interaction.reply({
                content: '❌ فقط المتحدى يمكنه قبول التحدي!',
                ephemeral: true
            });
        }

        // تحديث حالة التحدي
        database.updateChallengeStatus(challengeId, 'accepted');

        // إنشاء لعبة جديدة
        const game = new Game(challenge.channelId, challenger, challenged, challenge.difficulty);

        const embed = game.createGameEmbed();
        const buttons = game.createGameButtons();

        const message = await interaction.update({
            content: null,
            embeds: [embed],
            components: buttons,
            fetchReply: true
        });

        game.messageId = message.id;
        game.saveGame();

        // حذف التحدي
        database.deleteChallenge(challengeId);

        console.log(`✅ تم قبول التحدي وبدء اللعبة: ${game.id}`);

    } else if (action === 'decline') {
        // يجب أن يكون المتحدى هو من يرفض
        if (interaction.user.id !== challenge.challenged) {
            return interaction.reply({
                content: '❌ فقط المتحدى يمكنه رفض التحدي!',
                ephemeral: true
            });
        }

        // تحديث حالة التحدي
        database.updateChallengeStatus(challengeId, 'declined');

        const declinedEmbed = new EmbedBuilder()
            .setColor(config.COLORS.ERROR)
            .setTitle('❌ تم رفض التحدي')
            .setDescription(`**${challenged.displayName}** رفض تحدي **${challenger.displayName}**.`);

        await interaction.update({
            content: null,
            embeds: [declinedEmbed],
            components: []
        });

        // حذف التحدي
        database.deleteChallenge(challengeId);

        console.log(`❌ تم رفض التحدي: ${challenger.displayName} ضد ${challenged.displayName}`);

    } else if (action === 'cancel') {
        // يجب أن يكون المتحدي هو من يلغي
        if (interaction.user.id !== challenge.challenger) {
            return interaction.reply({
                content: '❌ فقط المتحدي يمكنه إلغاء التحدي!',
                ephemeral: true
            });
        }

        // تحديث حالة التحدي
        database.updateChallengeStatus(challengeId, 'cancelled');

        const cancelledEmbed = new EmbedBuilder()
            .setColor(config.COLORS.WARNING)
            .setTitle('🚫 تم إلغاء التحدي')
            .setDescription(`**${challenger.displayName}** ألغى التحدي.`);

        await interaction.update({
            content: null,
            embeds: [cancelledEmbed],
            components: []
        });

        // حذف التحدي
        database.deleteChallenge(challengeId);

        console.log(`🚫 تم إلغاء التحدي: ${challenger.displayName} ضد ${challenged.displayName}`);
    }
}

// معالجة الأخطاء
process.on('unhandledRejection', error => {
    console.error('❌ خطأ غير معالج:', error);
});

process.on('uncaughtException', error => {
    console.error('❌ استثناء غير معالج:', error);
    process.exit(1);
});

// تسجيل الدخول
client.login(config.BOT_TOKEN).catch(error => {
    console.error('❌ فشل في تسجيل الدخول:', error);
    console.log('💡 تأكد من أن BOT_TOKEN صحيح في ملف config.js');
});